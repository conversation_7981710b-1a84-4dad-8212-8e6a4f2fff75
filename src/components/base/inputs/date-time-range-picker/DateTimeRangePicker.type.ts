import type { StyledMultiInputDateTimeRangeField } from './styles';

/**
 * Props for the DateTimeRangePicker component
 */
export interface DateTimeRangePickerProps {
  /**
   * The current value of the date-time range picker, represented as a tuple of two dates.
   * Each date can be a Date object or null.
   */
  value: [Date | null, Date | null];

  /**
   * Callback function that is called when the date-time range changes.
   * @param range - A tuple containing the new start and end dates, each of which can be a Date object or null.
   */
  onChange: (range: [Date | null, Date | null]) => void;

  /**
   * The format string used to display the date-time values.
   */
  format?: string;

  /**
   * Determines whether the time should be displayed in 12-hour format with AM/PM.
   *
   * @type {boolean}
   * @property ampm - If true, the time will be displayed in 12-hour format with AM/PM. If false, it will be displayed in 24-hour format.
   */
  ampm?: boolean;

  /**
   * Represents localized text for the start and end labels in the date-time range picker.
   *
   * @property start - The label for the start date input field.
   * @property end - The label for the end date input field.
   */
  localeText?: { start: string; end: string };

  /**
   * Determines whether the date-time picker should close automatically after a date is selected.
   *
   * @type {boolean}
   * @property closeOnSelect - If true, the picker will close upon selecting a date.
   */
  closeOnSelect?: boolean;

  /**
   * Specifies the views available in the date-time picker.
   *
   * @type {Array<'day' | 'hours' | 'minutes' | 'seconds'>}
   * @property views - An array of strings representing the views to be displayed in the picker.
   */
  views?: Array<'day' | 'hours' | 'minutes' | 'seconds'>;

  /**
   * If true, disables the selection of future dates.
   */
  disableFuture?: boolean;

  /**
   * If true, disables the selection of past dates.
   */
  disablePast?: boolean;

  /**
   * Optional slots for customizing the component's elements.
   * @property field - A React component type used to render the input field.
   */
  slots?: {
    field: typeof StyledMultiInputDateTimeRangeField;
  };
}
