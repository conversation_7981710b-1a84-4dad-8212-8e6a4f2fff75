import { But<PERSON> } from '@/components/base/inputs/button';
import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';
import {
  AccordionDetails,
  AccordionSummary,
  Box,
  InputAdornment,
  TextField,
  type Theme,
} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import IconButton from '@mui/material/IconButton';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { FilterSmartSearchProps } from './FilterSmartSearch.type';

/**
 * A component that provides a smart search filter interface, allowing users to add and manage filters
 * based on predefined options. It supports input parsing, filter addition, and deletion.
 *
 * @param label - The label for the filter section, used for display purposes.
 * @param filterOptions - An array of available filter options, each with a key and label.
 * @param filters - The current list of active filters, each represented by a key-value pair.
 * @param onFiltersChange - A callback function that is triggered when the filters change.
 * @param defaultExpanded - A boolean indicating whether the accordion should be expanded by default.
 * @returns A JSX element representing the filter smart search component.
 */
export const FilterSmartSearch = ({
  label,
  filterOptions,
  filters,
  placeholder = 'e.g. status:active',
  onFiltersChange,
  defaultExpanded = true,
}: FilterSmartSearchProps & { defaultExpanded?: boolean }) => {
  const { t } = useTranslation();

  const [inputValue, setInputValue] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  /**
   * Parses input into { key, value } form by splitting on colon.
   */
  const parsedInput = useMemo(() => {
    const [rawKey, ...rest] = inputValue.split(':');
    return {
      key: rawKey?.trim() ?? '',
      value: rest.join(':').trim(),
    };
  }, [inputValue]);

  const isTypingField = !inputValue.includes(':');
  const isValidField = filterOptions.some((f) => f.key === parsedInput.key);

  /**
   * Handles changes in the autocomplete component.
   * This manages both the filter selection and input changes.
   */
  const handleChange = (_: unknown, newValue: string[] | string | null, reason: string) => {
    if (reason === 'removeOption' || reason === 'clear') {
      handleFilterRemoval(newValue);
    } else if (reason === 'selectOption') {
      if (Array.isArray(newValue)) {
        // Handle multiple selection (chip removal/addition)
        handleFilterRemoval(newValue);
      } else if (typeof newValue === 'string' && isTypingField) {
        // Handle single option selection for field names
        handleOptionSelection(newValue);
      }
    }
  };

  /**
   * Handles filter removal when chips are deleted
   */
  const handleFilterRemoval = (newValue: string[] | string | null) => {
    if (Array.isArray(newValue)) {
      const newFilters = newValue.map((val) => {
        const [key, ...rest] = val.split(':');
        return { key: (key || '').trim(), value: rest.join(':').trim() };
      });
      onFiltersChange(newFilters);
    } else if (newValue === null) {
      onFiltersChange([]);
    }
  };

  /**
   * Handles option selection from dropdown
   */
  const handleOptionSelection = (selectedValue: string) => {
    const matched = filterOptions.find((opt) => opt.label === selectedValue);
    if (matched) {
      setInputValue(`${matched.key}:`);
    }
  };

  /**
   * Creates a new filter from the current input
   */
  const createFilterFromInput = () => {
    const { key, value } = parsedInput;
    if (isValidField && value.trim()) {
      const exists = filters.some((f) => f.key === key && f.value === value);
      if (!exists) {
        onFiltersChange([...filters, { key, value }]);
        setInputValue('');
        return true;
      }
    }
    return false;
  };

  /**
   * Handles input value changes in the autocomplete field.
   */
  const handleInputChange = (_: unknown, newInputValue: string, reason: string) => {
    if (reason === 'input') {
      setInputValue(newInputValue);
    } else if (reason === 'clear') {
      setInputValue('');
    } else if (reason === 'selectOption') {
      // When user selects an option from dropdown, append colon for value input
      const matched = filterOptions.find((opt) => opt.label === newInputValue);
      if (matched) {
        setInputValue(`${matched.key}:`);
      } else {
        setInputValue(newInputValue);
      }
    }
  };

  /**
   * Handles keyboard events like Enter (add) and Backspace (delete last).
   */
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Backspace' && inputValue === '' && filters.length > 0) {
      e.preventDefault();
      const newFilters = [...filters];
      newFilters.pop();
      onFiltersChange(newFilters);
    }
  };

  /**
   * Toggles the visibility of the filters section.
   */
  const handleToggleFilters = () => {
    setShowFilters((prev) => !prev);
  };

  return (
    <Accordion
      defaultExpanded={defaultExpanded}
      customStyle="minimal"
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography
          variant="h5"
          caseTransform="sentenceCase"
        >
          {t(label)}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box
          px={{ xs: 2 }}
          pb={2}
        >
          <Autocomplete
            multiple
            freeSolo
            value={filters.map((f) => `${f.key}:${f.value}`)}
            inputValue={inputValue}
            onChange={(event, newValue, reason) => {
              if (reason === 'createOption' && inputValue.includes(':')) {
                // Handle Enter key press for creating filters
                createFilterFromInput();
              } else {
                handleChange(event, newValue, reason);
              }
            }}
            onInputChange={handleInputChange}
            onKeyDown={handleKeyDown}
            options={isTypingField ? filterOptions.map((opt) => opt.label) : []}
            filterOptions={(options, { inputValue: filterInput }) => {
              if (!isTypingField) return options;
              return options.filter((option) =>
                option.toLowerCase().includes(filterInput.toLowerCase())
              );
            }}
            selectOnFocus
            clearOnBlur={false}
            handleHomeEndKeys
            blurOnSelect={isTypingField}
            autoHighlight
            renderOption={(props, option) => {
              const { key, ...rest } = props;
              return (
                <li
                  key={key}
                  {...rest}
                >
                  <SearchIcon
                    fontSize="small"
                    sx={{ mr: 1, color: 'text.secondary' }}
                  />
                  {option}
                </li>
              );
            }}
            slotProps={{
              chip: {
                size: 'small',
                sx: {
                  maxWidth: 150,
                },
              },
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder={filters.length === 0 ? t(placeholder) : ''}
                aria-label={t('common.aria.search')}
                size="small"
                helperText={inputValue ? t('common.sentence.smartSearchFilterHelper') : ''}
                slotProps={{
                  input: {
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {filters.length > 0 && (
                          <InputAdornment position="end">
                            <IconButton
                              size="small"
                              aria-label={t('common.aria.clearAllFilters')}
                              onClick={() => {
                                onFiltersChange([]);
                                setInputValue('');
                              }}
                            >
                              <CloseIcon fontSize="small" />
                            </IconButton>
                          </InputAdornment>
                        )}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  },
                }}
              />
            )}
            sx={{ width: '100%' }}
          />

          {filters.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
              <Button
                onClick={handleToggleFilters}
                size="small"
                variant="text"
              >
                {showFilters ? 'Hide Filters' : 'View All Filters'}
              </Button>
            </Box>
          )}

          {showFilters && filters.length > 0 && (
            <Box
              sx={{
                mt: 1,
                background: (theme: Theme) => theme.palette.neutral?.[50] || theme.palette.grey[50],
                px: 1,
                py: 0.5,
                borderRadius: 1,
              }}
            >
              {filters.map((f) => (
                <Typography
                  key={`${f.key}:${f.value}`}
                  variant="body2"
                  color="textSecondary"
                  sx={{
                    mt: 0.5,
                    wordBreak: 'break-word',
                  }}
                >
                  {f.key}:{f.value}
                </Typography>
              ))}
            </Box>
          )}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export default FilterSmartSearch;
