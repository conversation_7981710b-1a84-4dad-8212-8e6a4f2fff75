import { But<PERSON> } from '@/components/base/inputs/button';
import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';
import {
  AccordionDetails,
  AccordionSummary,
  Box,
  Chip,
  InputAdornment,
  InputBase,
  TextField,
  type Theme,
} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import IconButton from '@mui/material/IconButton';
import { useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { FilterSmartSearchProps } from './FilterSmartSearch.type';

/**
 * A component that provides a smart search filter interface, allowing users to add and manage filters
 * based on predefined options. It supports input parsing, filter addition, and deletion.
 *
 * @param label - The label for the filter section, used for display purposes.
 * @param filterOptions - An array of available filter options, each with a key and label.
 * @param filters - The current list of active filters, each represented by a key-value pair.
 * @param onFiltersChange - A callback function that is triggered when the filters change.
 * @param defaultExpanded - A boolean indicating whether the accordion should be expanded by default.
 * @returns A JSX element representing the filter smart search component.
 */
export const FilterSmartSearch = ({
  label,
  filterOptions,
  filters,
  placeholder = 'e.g. status:active',
  onFiltersChange,
  defaultExpanded = true,
}: FilterSmartSearchProps & { defaultExpanded?: boolean }) => {
  const { t } = useTranslation();

  const [input, setInput] = useState('');
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [, setAnchorEl] = useState<HTMLElement | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  /**
   * Parses input into { key, value } form by splitting on colon.
   */
  const parsedInput = useMemo(() => {
    const [rawKey, ...rest] = input.split(':');
    return {
      key: rawKey?.trim() ?? '',
      value: rest.join(':').trim(),
    };
  }, [input]);

  const isTypingField = !input.includes(':');
  const isValidField = filterOptions.some((f) => f.key === parsedInput.key);

  /**
   * Adds a filter if the input is valid.
   */
  const handleAddFilter = () => {
    const { key, value } = parsedInput;
    if (!isValidField || !value) return;

    const exists = filters.some((f) => f.key === key && f.value === value);
    if (exists) return;

    const newFilters = [...filters, { key, value }];
    onFiltersChange(newFilters);
    setInput('');
  };

  /**
   * Removes a filter by key & value (ensures uniqueness without using index).
   */
  const handleDeleteFilter = (index: number) => {
    const updated = filters.filter((_, i) => i !== index);
    onFiltersChange(updated);
  };

  /**
   * Handles keyboard events like Enter (add) and Backspace (delete last).
   */
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddFilter();
    }

    if (e.key === 'Backspace' && input === '' && filters.length > 0) {
      handleDeleteFilter(filters.length - 1);
    }
  };

  /**
   * Sets the anchor element for the popper to the current input element.
   * This is used to position the popper relative to the input field.
   */
  const handleFocus = () => {
    setAnchorEl(inputRef.current);
  };

  /**
   * Toggles the visibility of the filters section.
   * It switches the state between showing and hiding the filters.
   */
  const handleToggleFilters = () => {
    setShowFilters((prev) => !prev);
  };

  /**
   * Handles changes in the autocomplete input field.
   * Updates the input state based on the selected or typed value.
   *
   * @param _ - The first parameter is unused and represents the event or context.
   * @param newValue - The new value selected or typed in the autocomplete input.
   */
  const handleAutocompleteChange = (_: unknown, newValue: string) => {
    const matched = filterOptions.find((opt) => opt.label === newValue);
    if (matched) {
      setInput(`${matched.key}:`);
    } else {
      setInput(newValue);
    }
  };

  return (
    <Accordion
      defaultExpanded={defaultExpanded}
      customStyle="minimal"
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography
          variant="h5"
          caseTransform="sentenceCase"
        >
          {t(label)}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box
          px={{ xs: 2 }}
          pb={2}
        >
          {/* {input && (
            <Typography
              variant="body2"
              color="textSecondary"
              sx={{ mb: 0.5 }}
            >
              {t('common.sentence.smartSearchFilterHelper')}
            </Typography>
          )} */}

          {/* <Box
            sx={{
              border: '1px solid #ccc',
              borderRadius: 1,
              width: '100%',
              display: 'flex',
              flexWrap: 'wrap',
              alignItems: 'center',
              gap: 0.5,
              px: 1,
              py: 1,
            }}
          > */}
            {/* {filters.map((f, i) => (
              <Chip
                // eslint-disable-next-line react/no-array-index-key
                key={`${f.key}:${f.value}-${i}`}
                label={`${f.key}:${f.value}`}
                size="small"
                onDelete={() => handleDeleteFilter(i)}
                title={`${f.key}:${f.value}`}
                aria-label={`Filter: ${f.key} is ${f.value}`}
                sx={{
                  maxWidth: 150,
                }}
              />
            ))} */}
            <Autocomplete
              multiple
              freeSolo
              disableClearable
              options={isTypingField ? filterOptions.map((opt) => opt.label) : []}
              value={input}
              inputValue={input}
              onInputChange={handleAutocompleteChange}
              onFocus={handleFocus}
              onKeyDown={(e) => handleInputKeyDown(e as React.KeyboardEvent<HTMLInputElement>)}
              renderOption={(props, option) => {
                const { key, ...rest } = props;
                return (
                  <li
                    key={key}
                    {...rest}
                  >
                    <SearchIcon
                      fontSize="small"
                      sx={{ mr: 1, color: 'text.secondary' }}
                    />
                    {option}
                  </li>
                );
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  inputRef={inputRef}
                  placeholder={t(placeholder)}
                  aria-label={t('common.aria.search')}
                  // variant="outlined"
                  size="small"
                  helperText={input ? t('common.sentence.smartSearchFilterHelper') : ''}
                  slotProps={{
                    ...params.InputProps,
                    input: {
                      endAdornment: (
                        <>
                          {filters.length > 0 && (
                            <InputAdornment position="end">
                              <IconButton
                                size="small"
                                aria-label={t('common.aria.clearAllFilters')}
                                onClick={() => {
                                  onFiltersChange([]);
                                  setInput('');
                                }}
                              >
                                <CloseIcon fontSize="small" />
                              </IconButton>
                            </InputAdornment>
                          )}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    },
                  }}
                />
              )}
              renderValue={(filters) => filters.map((f, i) => (
                <Chip
                  // eslint-disable-next-line react/no-array-index-key
                  key={`${f.key}:${f.value}-${i}`}
                  label={`${f.key}:${f.value}`}
                  size="small"
                  onDelete={() => handleDeleteFilter(i)}
                  title={`${f.key}:${f.value}`}
                  aria-label={`Filter: ${f.key} is ${f.value}`}
                  sx={{
                    maxWidth: 150,
                  }}
                />
              ))}
              sx={{ flex: 1 }}
            />
          // </Box>

          {filters.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                onClick={handleToggleFilters}
                size="small"
                variant="text"
              >
                {showFilters ? 'Hide Filters' : 'View All Filters'}
              </Button>
            </Box>
          )}

          {showFilters && filters.length > 0 && (
            <Typography
              sx={{
                mt: 0.5,
                ml: 0.5,
                background: (theme: Theme) => theme.palette.neutral[50],
                px: 1,
                py: 0.5,
                borderRadius: 1,
              }}
            >
              {filters.map((f, i) => (
                <Typography
                  key={`${f.key}:${f.value}`}
                  variant="body2"
                  color="textSecondary"
                  sx={{
                    mt: 0.5,
                    wordBreak: 'break-word',
                  }}
                >
                  {f.key}:{f.value}
                </Typography>
              ))}
            </Typography>
          )}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export default FilterSmartSearch;
