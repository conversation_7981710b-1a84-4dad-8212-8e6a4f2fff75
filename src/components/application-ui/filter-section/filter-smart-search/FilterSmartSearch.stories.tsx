import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import React, { useState } from 'react';
import { expect, userEvent, waitFor, within } from 'storybook/test';
import { FilterSmartSearch } from './FilterSmartSearch';

const meta: Meta<typeof FilterSmartSearch> = {
  title: 'Components/application-ui/filter-section/FilterSmartSearch',
  component: FilterSmartSearch,
  tags: ['autodocs'],
  args: {
    label: 'Search',
    defaultExpanded: true,
    filterOptions: [
      { key: 'requestId', label: 'requestId:' },
      { key: 'module', label: 'module:' },
      { key: 'event', label: 'event:' },
      { key: 'actor', label: 'actor:' },
      { key: 'targetRefId', label: 'targetRefId:' },
      { key: 'ipAddress', label: 'ipAddress:' },
      { key: 'hostAddress', label: 'hostAddress:' },
      { key: 'status', label: 'status:' },
    ],
  },
};

export default meta;
type Story = StoryObj<typeof FilterSmartSearch>;

export const Default: Story = {
  render: (args) => {
    const InteractiveFiltersWrapper = () => {
      const [filters, setFilters] = useState([
        { key: 'requestId', value: 'a81bc81b-dead-4e5d-abff-90865d1e13b1' },
        { key: 'module', value: 'auth' },
        { key: 'event', value: 'login' },
        { key: 'actor', value: 'admin' },
        { key: 'targetRefId', value: 'a81bc81b-dead-4e5d-abff-90865d1e13b1' },
        // eslint-disable-next-line sonarjs/no-hardcoded-ip
        { key: 'ipAddress', value: '***********' },
        { key: 'hostAddress', value: 'localhost' },
        { key: 'status', value: 'active' },
      ]);

      const handleFiltersChange = (newFilters: typeof filters) => {
        setFilters(newFilters);
      };

      return (
        <FilterSmartSearch
          {...args}
          filters={filters}
          onFiltersChange={handleFiltersChange}
        />
      );
    };

    return <InteractiveFiltersWrapper />;
  },

  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    const getInput = () => canvas.getByPlaceholderText(/e\.g\. status:active/i);

    await step('Input field should be present', async () => {
      await waitFor(() => {
        expect(getInput()).toBeInTheDocument();
      });
    });
    await step('Type in a new filter key and value', async () => {
      await userEvent.click(getInput());
      await userEvent.type(getInput(), 'event:register', { delay: 70 });
      await userEvent.keyboard('{Enter}');
    });

    await step('Verify chip for event:register appears', async () => {
      await waitFor(() => {
        expect(canvas.getByText('event:register')).toBeInTheDocument();
      });
      await userEvent.click(canvasElement);
    });

    await step('Press Backspace to remove last chip when input is empty', async () => {
      await userEvent.click(getInput());
      await userEvent.clear(getInput());

      await userEvent.keyboard('{Backspace}');

      await waitFor(() => {
        expect(canvas.queryByText('event:register')).not.toBeInTheDocument();
      });
    });

    await step('Toggle on view filter', async () => {
      const toggleButton = await canvas.findByRole('button', {
        name: /view all filters/i,
      });
      await userEvent.click(toggleButton);
      await waitFor(() => {
        expect(canvas.getByRole('button', { name: /hide filters/i })).toBeInTheDocument();
      });
    });

    await step('Toggle off view filter', async () => {
      const toggleButton = await canvas.findByRole('button', {
        name: /hide filters/i,
      });
      await userEvent.click(toggleButton);
      await new Promise((res) => setTimeout(res, 500));
      await expect(canvas.getByRole('button', { name: /view all filters/i })).toBeInTheDocument();
    });

    await step('Remove chip by clicking delete icon', async () => {
      const chip = canvas.getByText('module:auth');
      const container = chip.closest('div');

      if (!container) throw new Error('Chip container not found');

      const deleteButton = within(container).getByTestId('CancelIcon');
      await userEvent.click(deleteButton);

      await waitFor(() => {
        expect(canvas.queryByText('module:auth')).not.toBeInTheDocument();
      });
    });

    await step('Clear all filters with clear button', async () => {
      const clearButton = canvas.getByTestId('CloseIcon');
      await userEvent.click(clearButton);

      await waitFor(() => {
        expect(canvas.queryByText(/:/)).not.toBeInTheDocument();
      });

      await userEvent.click(canvasElement);
    });
  },
};
